<template>
  <div class="p-6 w-full h-full flex flex-col">
    <el-card class="border-0 shadow-sm w-full flex-1 flex flex-col">
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <el-button 
              type="default" 
              size="small" 
              circle 
              class="mr-3"
              @click="router.go(-1)"
            >
              <el-icon><ArrowLeft /></el-icon>
            </el-button>
            <h2 class="text-xl font-semibold text-gray-800">{{ currentKbase?.kbaseName || '文档列表' }}</h2>
          </div>
          <div class="flex space-x-2">
            <el-button type="primary" size="small" @click="showUploadDialog">
              <el-icon class="mr-1"><Upload /></el-icon>
              上传文档
            </el-button>
            <el-button type="default" size="small" @click="fetchFiles">
              <el-icon class="mr-1"><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 文档列表 -->
      <div class="w-full">
        <el-table
          :data="documents"
          style="width: 100%"
          v-loading="loading"
          empty-text="暂无文档"
          :header-cell-style="{ background: '#f8fafc' }"
        >
          <el-table-column prop="docName" label="文档名称" min-width="200">
            <template #default="{ row }">
              <div class="flex items-center">
                <el-icon class="mr-2 text-gray-500"><Document /></el-icon>
                <span>{{ row.docName }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="model" label="分割模式" width="150">
            <template #default="{ row }">
              <el-tag :type="getModelTagType()" size="small">
                {{ row.model || '默认' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="添加时间" width="180">
            <template #default="{ row }">
              {{ formatTime(row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="right">
            <template #default="{ row }">
              <el-button 
                size="small" 
                type="danger" 
                plain 
                @click="handleDelete(row.docId)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="mt-4 flex justify-end">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :background="true"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="fetchFiles"
            @current-change="fetchFiles"
          />
        </div>
      </div>

      <!-- 上传文档弹窗 -->
      <el-dialog
        v-model="uploadDialogVisible"
        title="上传文档"
        width="500px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <el-form :model="uploadForm" label-width="100px">
          <el-form-item label="分段模式">
            <el-radio-group v-model="uploadForm.modelType">
              <el-radio :value="'auto'">自动(auto)</el-radio>
              <el-radio :value="'chapter'">段落(chapter)</el-radio>
              <el-radio :value="'length'">长度(length)</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item 
            label="章节等级" 
            v-if="uploadForm.modelType === 'chapter'"
          >
            <el-input-number 
              v-model="uploadForm.chapterLevel" 
              :min="-1" 
              placeholder="默认-1"
            />
          </el-form-item>
          
          <el-form-item 
            label="分段长度" 
            v-if="uploadForm.modelType === 'length'"
          >
            <el-input-number 
              v-model="uploadForm.segmentLength" 
              :min="100" 
              :step="100" 
              placeholder="默认500"
            />
          </el-form-item>
          
          <el-form-item>
            <el-upload
              class="upload-demo"
              drag
              :http-request="uploadFile"
              :before-upload="beforeUpload"
              :show-file-list="false"
              multiple
              :disabled="uploading"
            >
              <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
              <div class="el-upload__text">
                <template v-if="!uploading">
                拖拽文件到此处或 <em>点击上传</em>
                </template>
                <template v-else>
                  <el-icon class="is-loading"><Loading /></el-icon>
                  正在上传中，请稍候...
                </template>
              </div>
              <template #tip>
                <div class="el-upload__tip">
                  支持上传PDF、Word、Excel、PPT、TXT等文档格式
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="uploadDialogVisible = false" :disabled="uploading">取消</el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  ArrowLeft, 
  Refresh, 
  Document, 
  Upload, 
  UploadFilled,
  Loading
} from '@element-plus/icons-vue'
import { 
  ElCard, 
  ElButton, 
  ElIcon, 
  ElTable, 
  ElTableColumn, 
  ElTag, 
  ElMessage,
  ElDialog,
  ElUpload,
  ElMessageBox,
  ElForm,
  ElFormItem,
  ElRadioGroup,
  ElRadio,
  ElInputNumber,
  ElPagination
} from 'element-plus'
import { showFileInKnowledgeBase, deleteFileInKnowledgeBase, uploadFileInKnowledgeBase } from '#/api/knowledge'

const route = useRoute()
const router = useRouter()

const kbaseId = ref(route.params.kbaseId as string)
const currentKbase = ref<any>(null)
const loading = ref(false)

// 上传表单数据
const uploadForm = ref({
  modelType: 'auto', // auto | chapter | length
  chapterLevel: -1,  // 默认-1
  segmentLength: 500 // 默认500
})

// 上传相关状态
const uploadDialogVisible = ref(false)

// 获取文档列表
const allDocuments = ref<any[]>([]) // 存储所有文档
const documents = ref<any[]>([])    // 存储当前页文档

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

const fetchFiles = async () => {
  try {
    loading.value = true
    const res = await showFileInKnowledgeBase(kbaseId.value)
    allDocuments.value = res.data.data || []
    
    // 排序逻辑
    allDocuments.value.sort((a, b) => {
      return a.docName.localeCompare(b.docName, 'zh-CN', { sensitivity: 'accent' })
    })
    
    // 客户端分页
    updatePagedDocuments()
  } catch (error) {
    console.error('获取文档列表失败:', error)
    ElMessage.error('获取文档列表失败')
  } finally {
    loading.value = false
  }
}

const updatePagedDocuments = () => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  documents.value = allDocuments.value.slice(start, end)
  total.value = allDocuments.value.length
}

// 监听分页变化
watch([currentPage, pageSize], () => {
  updatePagedDocuments()
})

// 格式化时间
const formatTime = (time: string) => {
  if (!time) return '未知时间'
  const date = new Date(time)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }).replace(/\//g, '-')
}

// 获取分割模式标签类型
// 获取分割模式标签类型 - 统一返回绿色(success)
const getModelTagType = (): 'success' => {
  return 'success'
}

// 显示上传弹窗
const showUploadDialog = () => {
  uploadDialogVisible.value = true
}

// 新增上传加载状态
const uploading = ref(false)

// 上传文件
const uploadFile = async (options: any) => {
  try {
    uploading.value = true  // 开始上传，显示加载状态
    
    let modelParam = ''
    
    if (uploadForm.value.modelType === 'chapter') {
      modelParam = `chapter:${uploadForm.value.chapterLevel}`
    } else if (uploadForm.value.modelType === 'length') {
      modelParam = `length:${uploadForm.value.segmentLength}`
    } else {
      modelParam = 'auto'
    }
    
    const res = await uploadFileInKnowledgeBase(
      kbaseId.value, 
      options.file, 
      modelParam
    )
    
    if (res.data.status === 200) {
      ElMessage.success({
        message: '上传成功',
        type: 'success'
      })
      // 延迟2秒后刷新列表
      setTimeout(() => {
        fetchFiles()
      }, 1000)
      
    } else {
      ElMessage.error(res.data.message || '上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败')
  } finally {
    uploading.value = false  // 上传结束，隐藏加载状态
    uploadDialogVisible.value = false // 关闭弹窗
  }
}

// 上传前校验
const beforeUpload = (file: File) => {
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain'
  ]
  
  if (!allowedTypes.includes(file.type)) {
    ElMessage.error('不支持的文件格式')
    return false
  }
  
  if (file.size > 50 * 1024 * 1024) { // 50MB
    ElMessage.error('文件大小不能超过50MB')
    return false
  }
  
  return true
}

// 删除文档
const handleDelete = async (docId: string) => {
  try {
    await ElMessageBox.confirm('确定要删除该文档吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await deleteFileInKnowledgeBase(docId)
    ElMessage.success('删除成功')
    fetchFiles()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}
// 在setup最开始检查参数
if (!route.params.kbaseId || route.params.kbaseId === 'undefined') {
  router.replace('/knowledge/list')
}

onMounted(() => {
  // 检查是否有kbaseId参数
  if (kbaseId.value == ':kbaseId') {
    router.push('/knowledge/list')
    return
  }
  fetchFiles()
})
</script>

<style scoped>
.el-card {
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

:deep(.el-card__header) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  padding: 16px 20px;
  background: transparent !important;
}

.upload-demo {
  text-align: center;
}

.el-table {
  margin-top: 10px;
}

.el-table :deep(th) {
  font-weight: 600;
  color: #64748b;
}

.el-pagination {
  margin-top: 20px;
  padding: 10px 0;
}
</style>
