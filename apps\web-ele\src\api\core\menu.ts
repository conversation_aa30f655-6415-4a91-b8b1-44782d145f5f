
/**
 * 获取用户所有菜单
 */
export async function getAllMenusApi() {
  return Promise.resolve([
    {
      path: '/knowledge',
      name: 'Knowledge',
      component: 'BasicLayout',
      meta: {
        title: '知识库',
        icon: 'ion:book-outline',
        order: 1
      },
      children: [
        {
          path: '/knowledge/list',
          name: 'KnowledgeList',
          component: 'views/knowledge/list.vue',
          meta: {
            title: '知识库列表',
            icon: 'ion:list-outline'
          }
        },
        {
          path: '/knowledge/file/:id',
          name: 'KnowledgeFile',
          component: 'views/knowledge/file.vue',
          meta: {
            title: '知识库文件',
            hideInMenu: true // 不在菜单显示
          }
        }
      ]
    }
  ])
}
