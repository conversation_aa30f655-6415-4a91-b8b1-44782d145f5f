<template>
  <div class="p-6 w-full h-full flex flex-col">
    <el-card class="border-0 shadow-sm w-full flex-1 flex flex-col">
      <template #header>
        <div class="flex items-center justify-between">
          <h2 class="text-xl font-semibold text-gray-800">知识库列表</h2>
          <div class="flex space-x-2">
            <el-button type="primary" size="small" @click="showCreateDialog">
              <el-icon class="mr-1"><Plus /></el-icon>
              新建知识库
            </el-button>
            <el-button type="default" size="small" @click="fetchKnowledgeBases">
              <el-icon class="mr-1"><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 知识库卡片列表 -->
      <div v-if="knowledgeBases.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <el-card 
          v-for="kb in knowledgeBases" 
          :key="kb.kbaseId"
          shadow="never"
          class="border border-gray-100 rounded-lg cursor-pointer group hover:border-primary-100 transition-all duration-300 w-full"
          :body-style="{ padding: 0 }"
          @click="goToFiles(kb.kbaseId)"
        >
          <!-- 操作按钮组 -->
          <div class="absolute right-2 top-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
            <el-button 
              size="small" 
              circle 
              @click.stop="showEditDialog(kb)"
              class="!bg-white !text-gray-600 hover:!bg-gray-50"
            >
              <el-icon><Edit /></el-icon>
            </el-button>
            <el-button 
              size="small" 
              circle 
              @click.stop="handleDelete(kb.kbaseId)"
              class="!bg-white !text-red-500 hover:!bg-gray-50"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>

          <div class="p-5">
            <div class="flex items-center mb-4">
              <div class="p-2 mr-3 rounded-lg bg-primary-50 text-primary-500">
                <el-icon size="18"><Notebook /></el-icon>
              </div>
              <span class="text-lg font-semibold text-gray-800 group-hover:text-primary-500 transition-colors">{{ kb.kbaseName }}</span>
            </div>
            <el-text truncated class="block mb-4 text-gray-500 text-sm leading-5 min-h-[40px]">
              {{ kb.desc || '暂无描述' }}
            </el-text>
            <div class="flex justify-between text-xs text-gray-400 mt-4 pt-3 border-t border-gray-100">
              <span class="flex items-center">
                <el-icon class="mr-1" size="12"><Document /></el-icon>
                {{ kb.currentDocs || 0 }} 个文件
              </span>
              <span class="flex items-center">
                <el-icon class="mr-1" size="12"><Clock /></el-icon>
                {{ formatTime(kb.updateTime) }}
              </span>
            </div>
          </div>
        </el-card>
      </div>
      
      <!-- 空状态 -->
      <el-empty v-else description="暂无知识库" class="py-10 w-full">
        <template #image>
          <el-icon size="60" class="text-gray-300"><FolderOpened /></el-icon>
        </template>
      </el-empty>


      <!-- 创建知识库弹窗 -->
      <el-dialog
        v-model="createDialogVisible"
        title="新建知识库"
        width="500px"
        :close-on-click-modal="false"
      >
        <el-form
          ref="createFormRef"
          :model="createForm"
          :rules="createRules"
          label-width="100px"
        >
          <el-form-item label="知识库名称" prop="kbase_name">
            <el-input
              v-model="createForm.kbase_name"
              placeholder="请输入知识库名称"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="知识库描述" prop="kbase_desc">
            <el-input
              v-model="createForm.kbase_desc"
              type="textarea"
              :rows="3"
              placeholder="请输入知识库描述"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="createDialogVisible = false">取消</el-button>
            <el-button
              type="primary"
              :loading="createLoading"
              @click="handleCreateKnowledgeBase"
            >
              确定
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 编辑知识库弹窗 -->
      <el-dialog
        v-model="editDialogVisible"
        title="编辑知识库"
        width="500px"
        :close-on-click-modal="false"
      >
        <el-form
          ref="editFormRef"
          :model="editForm"
          :rules="createRules"
          label-width="100px"
        >
          <el-form-item label="知识库名称" prop="kbase_name">
            <el-input
              v-model="editForm.kbase_name"
              placeholder="请输入知识库名称"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="知识库描述" prop="kbase_desc">
            <el-input
              v-model="editForm.kbase_desc"
              type="textarea"
              :rows="3"
              placeholder="请输入知识库描述"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="editDialogVisible = false">取消</el-button>
            <el-button
              type="primary"
              :loading="editLoading"
              @click="handleEditKnowledgeBase"
            >
              确定
            </el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { Notebook, Refresh, Document, Clock, FolderOpened, Plus, Edit, Delete } from '@element-plus/icons-vue'
import { ElCard, ElButton, ElIcon, ElText, ElEmpty, ElMessage, ElDialog, ElForm, ElFormItem, ElInput, ElMessageBox } from 'element-plus'
import { getKnowledgeBases, createKnowledgeBase, updateKnowledgeBase, deleteKnowledgeBase } from '#/api/knowledge'

const router = useRouter()



// 创建知识库相关状态
const createDialogVisible = ref(false)
const createLoading = ref(false)
const createFormRef = ref()
const createForm = ref({
  kbase_name: '',
  kbase_desc: ''
})

// 表单验证规则
const createRules = {
  kbase_name: [
    { required: true, message: '请输入知识库名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  kbase_desc: [
    { max: 200, message: '长度不能超过 200 个字符', trigger: 'blur' }
  ]
}

// 显示创建弹窗
const showCreateDialog = async () => {
  createForm.value = { kbase_name: '', kbase_desc: '' }
  createDialogVisible.value = true
  try {
    await nextTick()
    if (createFormRef.value) {
      createFormRef.value.clearValidate()
    }
  } catch (error) {
    console.error('清除表单验证失败:', error)
  }
}



const editDialogVisible = ref(false)
const editLoading = ref(false)
const editFormRef = ref()
const editForm = ref({
  kbase_id: '',
  kbase_name: '',
  kbase_desc: ''
})

// 显示编辑弹窗
const showEditDialog = (kb: any) => {
  editForm.value = {
    kbase_id: kb.kbaseId,
    kbase_name: kb.kbaseName,
    kbase_desc: kb.desc || ''
  }
  editDialogVisible.value = true
  nextTick(() => {
    if (editFormRef.value) {
      editFormRef.value.clearValidate()
    }
  })
}

// 编辑知识库
const handleEditKnowledgeBase = async () => {
  try {
    await editFormRef.value.validate()
    editLoading.value = true
    await updateKnowledgeBase(
      editForm.value.kbase_id,
      editForm.value.kbase_name,
      editForm.value.kbase_desc
    )
    ElMessage.success('修改知识库成功')
    editDialogVisible.value = false
    // 延迟2秒后刷新列表
    setTimeout(() => {
      fetchKnowledgeBases()
    }, 2000)
  } catch (error) {
    console.error('修改知识库失败:', error)
    ElMessage.error('修改知识库失败')
  } finally {
    editLoading.value = false
  }
}

const handleDelete = async (kbaseId: string) => {
  try {
    await ElMessageBox.confirm('确定要删除该知识库吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // 调用删除API
    await deleteKnowledgeBase(kbaseId)
    ElMessage.success('删除成功')
    // 延迟2秒后刷新列表
    setTimeout(() => {
      fetchKnowledgeBases()
    }, 2000)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}


// 创建知识库
const knowledgeBases = ref<any[]>([])
const loading = ref(false)
const handleCreateKnowledgeBase = async () => {
  try {
    await createFormRef.value.validate()
    createLoading.value = true
    await createKnowledgeBase(createForm.value.kbase_name, createForm.value.kbase_desc)
    ElMessage.success('创建知识库成功')
    createDialogVisible.value = false
    fetchKnowledgeBases() // 刷新列表
  } catch (error) {
    console.error('创建知识库失败:', error)
    ElMessage.error('创建知识库失败')
  } finally {
    createLoading.value = false
  }
}

// 首先定义知识库对象的类型
interface KnowledgeBase {
  kbaseId: string
  kbaseName: string
  desc?: string
  currentDocs?: number
  updateTime: string
  // 其他可能的字段...
}

// 获取知识库列表
const fetchKnowledgeBases = async () => {
  try {
    loading.value = true
    const res = await getKnowledgeBases()
    // 对知识库列表按照updateTime倒序排列
    knowledgeBases.value = res.data.data.sort((a: KnowledgeBase, b: KnowledgeBase) => {
      return new Date(b.updateTime).getTime() - new Date(a.updateTime).getTime()
    })
  } catch (error) {
    console.error('获取知识库列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 格式化时间（精确到秒）
const formatTime = (time: string) => {
  if (!time) return '未知时间'
  const date = new Date(time)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }).replace(/\//g, '-')
}

// 跳转到文件列表页
const goToFiles = (kbId: string) => {
  router.push(`/knowledge/file/${kbId}`)
}

onMounted(() => {
  fetchKnowledgeBases()
})
</script>

<style scoped>
.el-card {
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1); /* 更明显的边框颜色 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); /* 添加轻微阴影增强边框效果 */
}

.el-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(0, 0, 0, 0.15); /* 悬停时加深边框 */
}

/* 卡片内部内容区域 */
.el-card__body {
  background: transparent !important;
}

:deep(.el-card__header) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.08); /* 加深头部边框 */
  padding: 16px 20px;
  background: transparent !important;
}

/* 新增高度自适应样式 */
:deep(.el-card__body) {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

/* 优化滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.05);
}
</style>
