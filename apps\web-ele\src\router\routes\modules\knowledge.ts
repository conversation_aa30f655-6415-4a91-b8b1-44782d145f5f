import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:layout-dashboard',
      order: 1,
      title: $t('page.knowledge.title')
    },
    name: 'Knowledge',
    path: '/knowledge',
    redirect: '/knowledge/list',
    children: [
      {
        name: 'list',
        path: '/knowledge/list',
        component: () => import('#/views/knowledge/list.vue'),
        meta: {
          icon: 'ep:document', // 使用Element Plus图标
          title: $t('page.knowledge.list') // 需在语言文件配置
        }
      },
      {
        name: 'file',
        path: '/knowledge/file/:kbaseId',
        component: () => import('#/views/knowledge/file.vue'),
        meta: {
          icon: 'ep:document',
          title: $t('page.knowledge.file'), // 需在语言文件配置
          hideInMenu: true // 可选：在菜单中隐藏此路由
        }
      }
    ]
  }
];

export default routes;
