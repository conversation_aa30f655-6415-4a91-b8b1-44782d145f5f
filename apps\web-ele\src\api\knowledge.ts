import { baseRequestClient } from '#/api/request';

/*
 获取知识库列表
*/
export const getKnowledgeBases = () => {
  return baseRequestClient.post('http://**********:9252/kbase/list', {
    });
}


/*
 创建知识库
 @param kbase_name 知识库名称
 @param kbase_desc 知识库描述
*/
export const createKnowledgeBase = (kbase_name: string, kbase_desc: string) => {
  const formData = new FormData();
  formData.append('kbase_name', kbase_name);
  formData.append('kbase_desc', kbase_desc);
  
  return baseRequestClient.post('http://**********:9252/kbase/create', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/*
 修改知识库
 @param kbase_id 知识库id
 @param kbase_name 知识库名称
 @param kbase_desc 知识库描述
*/
export const updateKnowledgeBase = (kbase_id: string, kbase_name: string, kbase_desc: string) => {
  const formData = new FormData();
  formData.append('kbase_id', kbase_id);
  formData.append('kbase_name', kbase_name);
  formData.append('kbase_desc', kbase_desc);
  
  return baseRequestClient.post('http://**********:9252/kbase/update', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/*
 删除知识库
 @param kbase_id 知识库id
*/
export const deleteKnowledgeBase = (kbase_id: string) => {
  const formData = new FormData();
  formData.append('kbase_id', kbase_id);
  return baseRequestClient.post('http://**********:9252/kbase/delete', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/*
 知识库中文档列表
 @param kbase_id 知识库id
*/
export const showFileInKnowledgeBase = (kbase_id: string) => {
  const formData = new FormData();
  formData.append('kbase_id', kbase_id);
  return baseRequestClient.post('http://**********:9252/file/list', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/*
 知识库中文档上传
 @param kbase_id 知识库id
 @param file 文件
 @param model 分段方法
*/
export const uploadFileInKnowledgeBase = (kbase_id: string, file: File, model: string) => {
  const formData = new FormData();
  formData.append('kbase_id', kbase_id);
  formData.append('file', file);
  formData.append('model', model);
  
  return baseRequestClient.post('http://**********:9252/file/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/*
 知识库中文档删除
 @param doc_id 文档id
*/
export const deleteFileInKnowledgeBase = (doc_id: string) => {
  const formData = new FormData();
  formData.append('doc_id', doc_id);
  return baseRequestClient.post('http://**********:9252/file/delete', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 10 * 60 * 1000
  });
}
