import { baseRequestClient, requestClient } from '#/api/request';

export namespace AuthApi {
  /** 登录接口参数 */
  export interface LoginParams {
    password?: string;
    username?: string;
  }

  /** 登录接口返回值 */
  export interface LoginResult {
    accessToken: string;
  }

  export interface RefreshTokenResult {
    data: string;
    status: number;
  }
}

/**
 * 登录
 */
export async function loginApi(data: AuthApi.LoginParams) {
  // 固定账户验证
  if (data.username === 'admin' && data.password === 'Admin@123!') {
    return Promise.resolve({
      accessToken: 'fixed-demo-token',
      expiresIn: 86400
    });
  }
  return Promise.reject(new Error('用户名或密码错误'));
}


/**
 * 退出登录
 */
export async function logoutApi() {
  // 直接返回成功响应，不调用后端API
  return Promise.resolve({
    code: 200,
    message: '退出登录成功'
  });
}

/**
 * 获取用户权限码
 */
export async function getAccessCodesApi() {
  // 返回固定权限码
  return Promise.resolve(['*:*:*']); // 全部权限
}

export async function refreshTokenApi() {
  return Promise.resolve({
    data: 'fixed-demo-refresh-token',
    status: 200
  });
}
