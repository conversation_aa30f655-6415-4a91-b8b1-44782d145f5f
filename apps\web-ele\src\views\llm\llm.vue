<template>
  <div class="p-6 w-full h-full flex flex-col">
    <el-card class="border-0 shadow-sm w-full flex-1 flex flex-col">
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <el-button 
              type="default" 
              size="small" 
              circle 
              class="mr-3"
              @click="router.go(-1)"
            >
              <el-icon><ArrowLeft /></el-icon>
            </el-button>
            <h2 class="text-xl font-semibold text-gray-800">AI 助手</h2>
          </div>
          <div class="flex space-x-2">
            <el-button type="default" size="small" @click="clearChat">
              <el-icon class="mr-1"><Delete /></el-icon>
              清空对话
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 聊天区域 -->
      <div class="flex-1 overflow-y-auto p-4" ref="chatContainer">
        <div 
          v-for="(message, index) in messages" 
          :key="index" 
          class="mb-6"
          :class="{'text-right': message.role === 'user'}"
        >
          <div 
            class="inline-block max-w-3/4 px-4 py-3 rounded-lg"
            :class="{
              'bg-blue-100 text-blue-900': message.role === 'user',
              'bg-gray-100 text-gray-900': message.role === 'assistant'
            }"
          >
            <div class="flex items-start">
              <el-avatar 
                v-if="message.role === 'assistant'"
                class="mr-2"
                :size="32"
                src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"
              />
              <el-avatar 
                v-else
                class="mr-2"
                :size="32"
                src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
              />
              <div>
                <div class="font-medium mb-1">
                  {{ message.role === 'user' ? '我' : 'AI 助手' }}
                </div>
                <div class="whitespace-pre-wrap">{{ message.content }}</div>
                <div class="text-xs text-gray-500 mt-1">
                  {{ formatTime(message.timestamp) }}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div v-if="loading" class="text-center py-4">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span class="ml-2">AI 正在思考中...</span>
        </div>
      </div>
      
      <!-- 输入区域 -->
      <div class="p-4 border-t border-gray-200">
        <el-form @submit.prevent="sendMessage">
          <el-input
            v-model="inputMessage"
            type="textarea"
            :rows="3"
            placeholder="输入您的问题..."
            :disabled="loading"
            resize="none"
          />
          <div class="flex justify-end mt-2">
            <el-button 
              type="primary" 
              native-type="submit"
              :disabled="!inputMessage.trim() || loading"
            >
              发送
            </el-button>
          </div>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import { 
  ArrowLeft, 
  Delete,
  Loading
} from '@element-plus/icons-vue'
import { 
  ElCard, 
  ElButton, 
  ElIcon, 
  ElAvatar,
  ElInput,
  ElForm,
  ElMessage,
  ElMessageBox
} from 'element-plus'

const router = useRouter()

// 消息列表
interface Message {
  role: 'user' | 'assistant'
  content: string
  timestamp: number
}

const messages = ref<Message[]>([])
const inputMessage = ref('')
const loading = ref(false)
const chatContainer = ref<HTMLElement | null>(null)

// 初始化一些示例消息
onMounted(() => {
  messages.value = [
    {
      role: 'assistant',
      content: '您好！我是AI助手，有什么可以帮您的吗？',
      timestamp: Date.now()
    }
  ]
})

// 发送消息
const sendMessage = async () => {
  if (!inputMessage.value.trim()) return
  
  const userMessage: Message = {
    role: 'user',
    content: inputMessage.value,
    timestamp: Date.now()
  }
  
  messages.value.push(userMessage)
  inputMessage.value = ''
  
  // 滚动到底部
  scrollToBottom()
  
  // 模拟AI回复
  loading.value = true
  try {
    // 这里应该是调用AI API的地方
    // const response = await chatWithAI(userMessage.content)
    
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const aiMessage: Message = {
      role: 'assistant',
      content: getAIResponse(userMessage.content),
      timestamp: Date.now()
    }
    
    messages.value.push(aiMessage)
  } catch (error) {
    ElMessage.error('发送消息失败')
  } finally {
    loading.value = false
    scrollToBottom()
  }
}

// 简单的AI回复逻辑
const getAIResponse = (userMessage: string): string => {
  const lowerMsg = userMessage.toLowerCase()
  
  if (lowerMsg.includes('你好') || lowerMsg.includes('hi') || lowerMsg.includes('hello')) {
    return '你好！很高兴为您服务。'
  } else if (lowerMsg.includes('帮助') || lowerMsg.includes('help')) {
    return '我可以回答各种问题，请告诉我您需要什么帮助？'
  } else if (lowerMsg.includes('时间') || lowerMsg.includes('日期')) {
    return `当前时间是：${new Date().toLocaleString('zh-CN')}`
  } else if (lowerMsg.includes('天气')) {
    return '我无法获取实时天气数据，建议您查看天气预报应用。'
  } else {
    return `我已经收到您的消息："${userMessage}"。这是一个模拟回复，实际应用中我会调用AI API来生成更智能的回答。`
  }
}

// 清空聊天记录
const clearChat = () => {
  ElMessageBox.confirm('确定要清空聊天记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    messages.value = [{
      role: 'assistant',
      content: '聊天记录已清空，有什么可以帮您的吗？',
      timestamp: Date.now()
    }]
  }).catch(() => {})
}

// 格式化时间
const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (chatContainer.value) {
      chatContainer.value.scrollTop = chatContainer.value.scrollHeight
    }
  })
}

// 监听消息变化自动滚动
watch(messages, () => {
  scrollToBottom()
}, { deep: true })
</script>

<style scoped>
.el-card {
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

:deep(.el-card__header) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  padding: 16px 20px;
  background: transparent !important;
}

.chat-container {
  height: calc(100% - 150px);
  overflow-y: auto;
}

.el-avatar {
  flex-shrink: 0;
}
</style>
