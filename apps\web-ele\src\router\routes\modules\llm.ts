import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:layout-dashboard',
      order: 1,
      title: $t('page.llm.title')
    },
    name: 'llm',
    path: '/llm',
    redirect: '/llm',
    children: [
      {
        name: 'chat',
        path: '/llm/chat',
        component: () => import('#/views/llm/llm.vue'),
        meta: {
          icon: 'ep:document', // 使用Element Plus图标
          title: $t('page.llm.title') // 需在语言文件配置
        }
      }
    ]
  }
];

export default routes;
